// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

// use tauri::Manager; // 暂时不需要

// 自定义命令：获取应用版本
#[tauri::command]
fn get_app_version() -> String {
    env!("CARGO_PKG_VERSION").to_string()
}

// 自定义命令：打开外部链接
#[tauri::command]
async fn open_external_link(url: String) -> Result<(), String> {
    if let Err(e) = webbrowser::open(&url) {
        return Err(format!("无法打开链接: {}", e));
    }
    Ok(())
}

// 自定义命令：显示通知
#[tauri::command]
fn show_notification(title: String, body: String) -> Result<(), String> {
    #[cfg(target_os = "windows")]
    {
        use std::process::Command;
        let _ = Command::new("powershell")
            .args(&[
                "-Command",
                &format!(
                    "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.MessageBox]::Show('{}', '{}')",
                    body, title
                ),
            ])
            .output();
    }

    #[cfg(not(target_os = "windows"))]
    {
        println!("通知: {} - {}", title, body);
    }

    Ok(())
}

// Tauri 2.0 不再支持传统菜单，改用插件系统

fn main() {
    tauri::Builder::default()
        .invoke_handler(tauri::generate_handler![
            get_app_version,
            open_external_link,
            show_notification
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
