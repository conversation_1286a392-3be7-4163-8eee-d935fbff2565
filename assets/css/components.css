/* 组件样式文件 */

/* 导航组件 */
.nav-sidebar {
    background: var(--bg-card);
    border-right: 1px solid var(--border-color);
    backdrop-filter: blur(20px);
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.nav-item:hover {
    background: var(--bg-secondary);
}

.nav-item.active {
    background: var(--accent-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.nav-item .icon {
    margin-right: 0.75rem;
    font-size: 1.25rem;
}

.nav-item .badge {
    margin-left: auto;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.nav-item.active .badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

/* 卡片组件 */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    color: var(--text-secondary);
    line-height: 1.6;
}

.card-footer {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
    cursor: pointer;
    border: none;
    font-size: 0.875rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

.btn-primary {
    background: var(--accent-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-primary);
    border-color: var(--accent-color);
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-error {
    background: var(--error-color);
    color: white;
}

.btn-ghost {
    background: transparent;
    color: var(--text-primary);
}

.btn-ghost:hover:not(:disabled) {
    background: var(--bg-secondary);
}

/* 表单组件 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-muted);
}

.form-error {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--error-color);
}

/* 模态框组件 */
.modal-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal {
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 通知组件 */
.notification {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
    max-width: 400px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification-header {
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification-icon {
    font-size: 1.25rem;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.notification-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.notification.success {
    border-left: 4px solid var(--success-color);
}

.notification.warning {
    border-left: 4px solid var(--warning-color);
}

.notification.error {
    border-left: 4px solid var(--error-color);
}

.notification.info {
    border-left: 4px solid var(--info-color);
}

/* 标签组件 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.tag-primary {
    background: rgba(59, 130, 246, 0.1);
    color: var(--accent-color);
    border-color: rgba(59, 130, 246, 0.2);
}

.tag-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border-color: rgba(16, 185, 129, 0.2);
}

.tag-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border-color: rgba(245, 158, 11, 0.2);
}

.tag-error {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border-color: rgba(239, 68, 68, 0.2);
}

/* 进度条组件 */
.progress {
    width: 100%;
    height: 8px;
    background: var(--bg-secondary);
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: var(--accent-color);
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-bar.success {
    background: var(--success-color);
}

.progress-bar.warning {
    background: var(--warning-color);
}

.progress-bar.error {
    background: var(--error-color);
}

/* 加载器组件 */
.loader {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.loader-lg {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .modal {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
    
    .notification {
        left: 1rem;
        right: 1rem;
        max-width: none;
    }
    
    .card {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.625rem 1rem;
    }
}
