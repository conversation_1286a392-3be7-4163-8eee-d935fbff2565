/* 富文本编辑器样式 */
.rich-text-editor {
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    background: white;
    overflow: hidden;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rich-text-editor:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 工具栏样式 */
.editor-toolbar {
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    padding: 8px 12px;
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
}

.toolbar-group {
    display: flex;
    gap: 2px;
    padding: 0 8px;
    border-right: 1px solid #e5e7eb;
}

.toolbar-group:last-child {
    border-right: none;
}

.toolbar-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background: transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4b5563;
    font-size: 14px;
}

.toolbar-button:hover {
    background: #e5e7eb;
    color: #1f2937;
}

.toolbar-button.active {
    background: #3b82f6;
    color: white;
}

.toolbar-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.toolbar-select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
}

.toolbar-select:focus {
    outline: none;
    border-color: #3b82f6;
}

/* 编辑区域样式 */
.editor-content {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    padding: 16px;
    font-size: 14px;
    line-height: 1.6;
    color: #374151;
    background: white;
}

.editor-content:focus {
    outline: none;
}

/* 编辑器内容样式 */
.editor-content h1 {
    font-size: 24px;
    font-weight: 700;
    margin: 16px 0 8px 0;
    color: #1f2937;
}

.editor-content h2 {
    font-size: 20px;
    font-weight: 600;
    margin: 14px 0 6px 0;
    color: #1f2937;
}

.editor-content h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 12px 0 4px 0;
    color: #1f2937;
}

.editor-content p {
    margin: 8px 0;
}

.editor-content strong {
    font-weight: 600;
}

.editor-content em {
    font-style: italic;
}

.editor-content u {
    text-decoration: underline;
}

.editor-content s {
    text-decoration: line-through;
}

.editor-content ul, .editor-content ol {
    margin: 8px 0;
    padding-left: 24px;
}

.editor-content li {
    margin: 4px 0;
}

.editor-content blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 16px;
    margin: 16px 0;
    color: #6b7280;
    font-style: italic;
}

.editor-content code {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', monospace;
    font-size: 13px;
    color: #dc2626;
}

.editor-content pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 16px 0;
    font-family: 'JetBrains Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
}

.editor-content pre code {
    background: transparent;
    padding: 0;
    color: inherit;
}

.editor-content a {
    color: #3b82f6;
    text-decoration: underline;
}

.editor-content a:hover {
    color: #1d4ed8;
}

.editor-content img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 8px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 图片上传区域 */
.image-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    background: #f9fafb;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.image-upload-area.dragover {
    border-color: #3b82f6;
    background: #dbeafe;
}

/* 链接编辑弹窗 */
.link-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.link-modal-content {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 400px;
}

.link-modal h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.link-modal input {
    width: 100%;
    padding: 12px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    margin-bottom: 16px;
    font-size: 14px;
}

.link-modal input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.link-modal-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.link-modal-buttons button {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.link-modal-buttons .btn-primary {
    background: #3b82f6;
    color: white;
}

.link-modal-buttons .btn-primary:hover {
    background: #1d4ed8;
}

.link-modal-buttons .btn-secondary {
    background: #f3f4f6;
    color: #374151;
}

.link-modal-buttons .btn-secondary:hover {
    background: #e5e7eb;
}

/* 颜色选择器 */
.color-picker {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 4px;
    padding: 8px;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    position: absolute;
    z-index: 100;
}

.color-option {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.2s ease;
}

.color-option:hover {
    transform: scale(1.1);
    border-color: #374151;
}

/* 搜索高亮样式 */
.search-highlight {
    background: #fef08a;
    color: #92400e;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

/* 图片上传模态框 */
.image-upload-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-upload-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.image-upload-modal .modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
}

.image-upload-modal .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.image-upload-modal .modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.image-upload-modal .modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.image-upload-modal .modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.image-upload-modal .modal-body {
    padding: 20px;
}

/* 上传标签页 */
.upload-tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 20px;
}

.tab-button {
    flex: 1;
    padding: 12px 16px;
    background: none;
    border: none;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-button.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
}

.tab-button:hover {
    color: #374151;
}

/* 上传区域 */
.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f9fafb;
}

.upload-area:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.upload-area.drag-over {
    border-color: #3b82f6;
    background: #eff6ff;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.upload-area p {
    margin: 8px 0;
    color: #374151;
}

.upload-hint {
    font-size: 12px;
    color: #6b7280;
}

/* 图片预览 */
.image-preview {
    text-align: center;
}

.image-preview img {
    border-radius: 8px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* 表单元素 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
}

.url-preview {
    margin: 16px 0;
    text-align: center;
}

.url-preview img {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 便签卡片样式 */
.note-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.note-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

.note-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.note-card:hover::before {
    opacity: 1;
}

/* 便签颜色主题 */
.note-yellow {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    border-color: #f59e0b;
}

.note-blue {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border-color: #3b82f6;
}

.note-green {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    border-color: #10b981;
}

.note-pink {
    background: linear-gradient(135deg, #fce7f3 0%, #fbcfe8 100%);
    border-color: #ec4899;
}

.note-purple {
    background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);
    border-color: #8b5cf6;
}

.note-orange {
    background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);
    border-color: #f97316;
}

.note-gray {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    border-color: #6b7280;
}

/* 文本截断样式 */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 便签容器样式 */
.notes-container {
    min-height: 400px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .editor-toolbar {
        padding: 6px 8px;
    }

    .toolbar-group {
        padding: 0 4px;
    }

    .toolbar-button {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }

    .editor-content {
        padding: 12px;
        font-size: 13px;
    }

    .link-modal-content {
        padding: 16px;
        margin: 16px;
    }

    .note-card {
        margin-bottom: 16px;
    }
}
