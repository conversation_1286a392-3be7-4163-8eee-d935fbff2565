// 便签模板服务
class NotesTemplateService {
    constructor() {
        this.templates = this.getDefaultTemplates();
        this.customTemplates = this.loadCustomTemplates();
    }

    // 获取默认模板
    getDefaultTemplates() {
        return [
            {
                id: 'daily-plan',
                name: '日计划',
                icon: '📅',
                category: 'productivity',
                description: '每日工作计划模板',
                content: `
                    <h3>📅 ${this.formatDate(new Date())} 日计划</h3>
                    <h4>🎯 今日目标</h4>
                    <ul>
                        <li>目标1</li>
                        <li>目标2</li>
                        <li>目标3</li>
                    </ul>

                    <h4>📋 任务清单</h4>
                    <ul>
                        <li>□ 重要任务1</li>
                        <li>□ 重要任务2</li>
                        <li>□ 一般任务1</li>
                    </ul>

                    <h4>📝 备注</h4>
                    <p>今日重点关注事项...</p>
                `
            },
            {
                id: 'meeting-notes',
                name: '会议记录',
                icon: '🤝',
                category: 'work',
                description: '会议记录模板',
                content: `
                    <h3>🤝 会议记录</h3>
                    <p><strong>会议主题：</strong></p>
                    <p><strong>时间：</strong> ${this.formatDateTime(new Date())}</p>
                    <p><strong>参会人员：</strong></p>
                    <p><strong>会议地点：</strong></p>

                    <h4>📋 会议议程</h4>
                    <ol>
                        <li>议题1</li>
                        <li>议题2</li>
                        <li>议题3</li>
                    </ol>

                    <h4>💬 讨论要点</h4>
                    <ul>
                        <li>要点1</li>
                        <li>要点2</li>
                    </ul>

                    <h4>✅ 行动项</h4>
                    <ul>
                        <li>□ 行动项1 - 负责人：</li>
                        <li>□ 行动项2 - 负责人：</li>
                    </ul>

                    <h4>📝 其他备注</h4>
                    <p></p>
                `
            },
            {
                id: 'study-notes',
                name: '学习笔记',
                icon: '📚',
                category: 'study',
                description: '学习笔记模板',
                content: `
                    <h3>📚 学习笔记</h3>
                    <p><strong>学习主题：</strong></p>
                    <p><strong>学习时间：</strong> ${this.formatDateTime(new Date())}</p>
                    <p><strong>学习来源：</strong></p>

                    <h4>📖 主要内容</h4>
                    <ul>
                        <li>知识点1</li>
                        <li>知识点2</li>
                        <li>知识点3</li>
                    </ul>

                    <h4>💡 重点理解</h4>
                    <p>重要概念和理解...</p>

                    <h4>❓ 疑问点</h4>
                    <ul>
                        <li>疑问1</li>
                        <li>疑问2</li>
                    </ul>

                    <h4>🔗 相关资源</h4>
                    <ul>
                        <li><a href="#">参考链接1</a></li>
                        <li><a href="#">参考链接2</a></li>
                    </ul>

                    <h4>📝 总结</h4>
                    <p>学习总结...</p>
                `
            },
            {
                id: 'project-plan',
                name: '项目计划',
                icon: '🚀',
                category: 'work',
                description: '项目计划模板',
                content: `
                    <h3>🚀 项目计划</h3>
                    <p><strong>项目名称：</strong></p>
                    <p><strong>项目负责人：</strong></p>
                    <p><strong>开始时间：</strong> ${this.formatDate(new Date())}</p>
                    <p><strong>预计完成：</strong></p>

                    <h4>🎯 项目目标</h4>
                    <p>项目的主要目标和期望成果...</p>

                    <h4>📋 主要任务</h4>
                    <ol>
                        <li>□ 需求分析</li>
                        <li>□ 方案设计</li>
                        <li>□ 开发实现</li>
                        <li>□ 测试验证</li>
                        <li>□ 上线部署</li>
                    </ol>

                    <h4>👥 团队成员</h4>
                    <ul>
                        <li>成员1 - 职责</li>
                        <li>成员2 - 职责</li>
                    </ul>

                    <h4>⚠️ 风险评估</h4>
                    <ul>
                        <li>风险1 - 应对措施</li>
                        <li>风险2 - 应对措施</li>
                    </ul>
                `
            },
            {
                id: 'weekly-review',
                name: '周总结',
                icon: '📊',
                category: 'productivity',
                description: '周工作总结模板',
                content: `
                    <h3>📊 周总结</h3>
                    <p><strong>时间范围：</strong> ${this.getWeekRange()}</p>

                    <h4>✅ 本周完成</h4>
                    <ul>
                        <li>完成事项1</li>
                        <li>完成事项2</li>
                        <li>完成事项3</li>
                    </ul>

                    <h4>🔄 进行中</h4>
                    <ul>
                        <li>进行中事项1</li>
                        <li>进行中事项2</li>
                    </ul>

                    <h4>⏰ 下周计划</h4>
                    <ul>
                        <li>□ 下周任务1</li>
                        <li>□ 下周任务2</li>
                        <li>□ 下周任务3</li>
                    </ul>

                    <h4>💡 经验总结</h4>
                    <p>本周的收获和经验...</p>

                    <h4>🎯 改进计划</h4>
                    <p>下周需要改进的地方...</p>
                `
            },
            {
                id: 'idea-capture',
                name: '灵感记录',
                icon: '💡',
                category: 'creative',
                description: '灵感和创意记录模板',
                content: `
                    <h3>💡 灵感记录</h3>
                    <p><strong>记录时间：</strong> ${this.formatDateTime(new Date())}</p>
                    <p><strong>灵感来源：</strong></p>

                    <h4>🌟 核心想法</h4>
                    <p>主要的灵感和想法...</p>

                    <h4>🔍 详细描述</h4>
                    <p>详细展开这个想法...</p>

                    <h4>🎯 应用场景</h4>
                    <ul>
                        <li>应用场景1</li>
                        <li>应用场景2</li>
                    </ul>

                    <h4>📝 后续行动</h4>
                    <ul>
                        <li>□ 进一步研究</li>
                        <li>□ 制作原型</li>
                        <li>□ 寻找合作伙伴</li>
                    </ul>

                    <h4>🔗 相关资源</h4>
                    <p>相关的链接、文档或参考资料...</p>
                `
            }
        ];
    }

    // 格式化日期
    formatDate(date) {
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            weekday: 'long'
        });
    }

    // 格式化日期时间
    formatDateTime(date) {
        return date.toLocaleString('zh-CN');
    }

    // 获取本周时间范围
    getWeekRange() {
        const now = new Date();
        const monday = new Date(now);
        monday.setDate(now.getDate() - now.getDay() + 1);

        const sunday = new Date(monday);
        sunday.setDate(monday.getDate() + 6);

        return `${monday.toLocaleDateString('zh-CN')} - ${sunday.toLocaleDateString('zh-CN')}`;
    }

    // 加载自定义模板
    loadCustomTemplates() {
        try {
            const saved = localStorage.getItem('notes_custom_templates');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('加载自定义模板失败:', error);
            return [];
        }
    }

    // 保存自定义模板
    saveCustomTemplates() {
        try {
            localStorage.setItem('notes_custom_templates', JSON.stringify(this.customTemplates));
        } catch (error) {
            console.error('保存自定义模板失败:', error);
        }
    }

    // 获取所有模板
    getAllTemplates() {
        return [...this.templates, ...this.customTemplates];
    }

    // 根据分类获取模板
    getTemplatesByCategory(category) {
        return this.getAllTemplates().filter(template => template.category === category);
    }

    // 获取模板分类
    getTemplateCategories() {
        const categories = new Set();
        this.getAllTemplates().forEach(template => {
            categories.add(template.category);
        });
        return Array.from(categories);
    }

    // 获取模板
    getTemplate(id) {
        return this.getAllTemplates().find(template => template.id === id);
    }

    // 添加自定义模板
    addCustomTemplate(template) {
        const newTemplate = {
            id: 'custom_' + Date.now(),
            ...template,
            isCustom: true,
            createdAt: new Date().toISOString()
        };

        this.customTemplates.push(newTemplate);
        this.saveCustomTemplates();
        return newTemplate;
    }

    // 更新自定义模板
    updateCustomTemplate(id, updates) {
        const index = this.customTemplates.findIndex(template => template.id === id);
        if (index > -1) {
            this.customTemplates[index] = {
                ...this.customTemplates[index],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            this.saveCustomTemplates();
            return this.customTemplates[index];
        }
        return null;
    }

    // 删除自定义模板
    deleteCustomTemplate(id) {
        const index = this.customTemplates.findIndex(template => template.id === id);
        if (index > -1) {
            this.customTemplates.splice(index, 1);
            this.saveCustomTemplates();
            return true;
        }
        return false;
    }

    // 应用模板到便签
    applyTemplate(templateId, noteData = {}) {
        const template = this.getTemplate(templateId);
        if (!template) {
            throw new Error('模板不存在');
        }

        return {
            title: noteData.title || template.name,
            content: template.content,
            color: noteData.color || 'yellow',
            category: noteData.category || template.category || 'work',
            tags: noteData.tags || [template.name],
            isPinned: false,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
    }

    // 从便签创建模板
    createTemplateFromNote(note, templateData) {
        const template = {
            name: templateData.name || note.title,
            icon: templateData.icon || '📝',
            category: templateData.category || note.category || 'custom',
            description: templateData.description || '自定义模板',
            content: note.content
        };

        return this.addCustomTemplate(template);
    }

    // 搜索模板
    searchTemplates(query) {
        if (!query) return this.getAllTemplates();

        const searchTerm = query.toLowerCase();
        return this.getAllTemplates().filter(template => {
            return template.name.toLowerCase().includes(searchTerm) ||
                   template.description.toLowerCase().includes(searchTerm) ||
                   template.category.toLowerCase().includes(searchTerm);
        });
    }

    // 获取模板预览
    getTemplatePreview(templateId, maxLength = 100) {
        const template = this.getTemplate(templateId);
        if (!template) return '';

        // 移除HTML标签并截取预览文本
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = template.content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';

        return textContent.length > maxLength
            ? textContent.substring(0, maxLength) + '...'
            : textContent;
    }

    // 导出模板
    exportTemplates(templateIds = null) {
        const templatesToExport = templateIds
            ? this.getAllTemplates().filter(t => templateIds.includes(t.id))
            : this.customTemplates;

        return {
            version: '1.0',
            exportDate: new Date().toISOString(),
            templates: templatesToExport
        };
    }

    // 导入模板
    importTemplates(data) {
        if (!data.templates || !Array.isArray(data.templates)) {
            throw new Error('无效的模板数据格式');
        }

        const imported = [];
        data.templates.forEach(template => {
            try {
                const newTemplate = this.addCustomTemplate({
                    name: template.name,
                    icon: template.icon || '📝',
                    category: template.category || 'imported',
                    description: template.description || '导入的模板',
                    content: template.content
                });
                imported.push(newTemplate);
            } catch (error) {
                console.error('导入模板失败:', template.name, error);
            }
        });

        return imported;
    }
}

// 导出服务
window.NotesTemplateService = NotesTemplateService;
