// Tauri 集成脚本
// 提供 Tauri 特定的功能和 API 封装

class TauriIntegration {
    constructor() {
        this.isTauri = window.__TAURI__ !== undefined;
        this.api = null;
        
        if (this.isTauri) {
            this.initTauriAPI();
        }
    }

    async initTauriAPI() {
        try {
            // 动态导入 Tauri API
            const { invoke } = window.__TAURI__.tauri;
            const { open } = window.__TAURI__.shell;
            const { save, open: openDialog } = window.__TAURI__.dialog;
            const { writeText, readText } = window.__TAURI__.clipboard;
            const { sendNotification } = window.__TAURI__.notification;
            
            this.api = {
                invoke,
                shell: { open },
                dialog: { save, open: openDialog },
                clipboard: { writeText, readText },
                notification: { sendNotification }
            };
            
            console.log('✅ Tauri API 初始化成功');
            this.setupTauriFeatures();
        } catch (error) {
            console.error('❌ Tauri API 初始化失败:', error);
        }
    }

    setupTauriFeatures() {
        // 监听菜单事件
        this.listenToMenuEvents();
        
        // 设置窗口标题
        this.setWindowTitle('智能工作台');
        
        // 禁用右键菜单（可选）
        // document.addEventListener('contextmenu', e => e.preventDefault());
    }

    listenToMenuEvents() {
        if (!this.isTauri) return;

        const { listen } = window.__TAURI__.event;
        
        // 监听菜单事件
        listen('menu-new', () => {
            console.log('菜单：新建');
            // 触发新建操作
            if (window.app && window.app.showAddSiteModal !== undefined) {
                window.app.showAddSiteModal = true;
                window.app.resetSiteForm();
            }
        });

        listen('menu-import', () => {
            console.log('菜单：导入书签');
            // 触发导入操作
            if (window.app && window.app.showImportModal !== undefined) {
                window.app.showImportModal = true;
            }
        });

        listen('menu-export', () => {
            console.log('菜单：导出书签');
            // 触发导出操作
            if (window.app && window.app.exportBookmarks) {
                window.app.exportBookmarks();
            }
        });
    }

    setWindowTitle(title) {
        if (!this.isTauri) {
            document.title = title;
            return;
        }

        try {
            const { appWindow } = window.__TAURI__.window;
            appWindow.setTitle(title);
        } catch (error) {
            console.warn('设置窗口标题失败:', error);
            document.title = title;
        }
    }

    // 打开外部链接
    async openExternal(url) {
        if (!this.isTauri) {
            window.open(url, '_blank');
            return;
        }

        try {
            await this.api.shell.open(url);
        } catch (error) {
            console.error('打开外部链接失败:', error);
            // 降级到普通方式
            window.open(url, '_blank');
        }
    }

    // 显示通知
    async showNotification(title, body) {
        if (!this.isTauri) {
            // 浏览器通知
            if ('Notification' in window) {
                if (Notification.permission === 'granted') {
                    new Notification(title, { body });
                } else if (Notification.permission !== 'denied') {
                    const permission = await Notification.requestPermission();
                    if (permission === 'granted') {
                        new Notification(title, { body });
                    }
                }
            }
            return;
        }

        try {
            await this.api.notification.sendNotification({
                title,
                body
            });
        } catch (error) {
            console.error('显示通知失败:', error);
        }
    }

    // 复制到剪贴板
    async copyToClipboard(text) {
        if (!this.isTauri) {
            // 浏览器方式
            try {
                await navigator.clipboard.writeText(text);
                return true;
            } catch (error) {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                const success = document.execCommand('copy');
                document.body.removeChild(textArea);
                return success;
            }
        }

        try {
            await this.api.clipboard.writeText(text);
            return true;
        } catch (error) {
            console.error('复制到剪贴板失败:', error);
            return false;
        }
    }

    // 从剪贴板读取
    async readFromClipboard() {
        if (!this.isTauri) {
            try {
                return await navigator.clipboard.readText();
            } catch (error) {
                console.error('从剪贴板读取失败:', error);
                return '';
            }
        }

        try {
            return await this.api.clipboard.readText();
        } catch (error) {
            console.error('从剪贴板读取失败:', error);
            return '';
        }
    }

    // 保存文件对话框
    async saveFileDialog(defaultPath, filters) {
        if (!this.isTauri) {
            // 浏览器降级：创建下载链接
            return null;
        }

        try {
            return await this.api.dialog.save({
                defaultPath,
                filters
            });
        } catch (error) {
            console.error('保存文件对话框失败:', error);
            return null;
        }
    }

    // 打开文件对话框
    async openFileDialog(filters, multiple = false) {
        if (!this.isTauri) {
            // 浏览器降级：使用 input[type="file"]
            return new Promise((resolve) => {
                const input = document.createElement('input');
                input.type = 'file';
                input.multiple = multiple;
                if (filters && filters.length > 0) {
                    input.accept = filters.map(f => f.extensions.map(ext => `.${ext}`).join(',')).join(',');
                }
                input.onchange = (e) => {
                    resolve(multiple ? Array.from(e.target.files) : e.target.files[0]);
                };
                input.click();
            });
        }

        try {
            return await this.api.dialog.open({
                filters,
                multiple
            });
        } catch (error) {
            console.error('打开文件对话框失败:', error);
            return null;
        }
    }

    // 获取应用版本
    async getAppVersion() {
        if (!this.isTauri) {
            return '1.0.0';
        }

        try {
            return await this.api.invoke('get_app_version');
        } catch (error) {
            console.error('获取应用版本失败:', error);
            return '1.0.0';
        }
    }
}

// 创建全局实例
window.tauriIntegration = new TauriIntegration();

// 导出给其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TauriIntegration;
}
