// 应用常量配置
window.APP_CONFIG = {
    // 应用信息
    APP_NAME: '智能导航中心',
    APP_VERSION: '2.0.0',
    APP_DESCRIPTION: '现代化的多功能导航和管理平台',
    
    // 功能分类
    CATEGORIES: [
        {
            id: 'navigation',
            name: '网页导航',
            icon: '🌐',
            description: '网站收藏和快速访问',
            component: 'navigation'
        },
        {
            id: 'notes',
            name: '便签记录',
            icon: '📝',
            description: '快速记录和管理便签',
            component: 'notes'
        },
        {
            id: 'todos',
            name: '待办事项',
            icon: '✅',
            description: '任务管理和进度跟踪',
            component: 'todos'
        },
        {
            id: 'calendar',
            name: '日程管理',
            icon: '📅',
            description: '日程安排和提醒',
            component: 'calendar'
        },
        {
            id: 'timer',
            name: '专注计时',
            icon: '⏰',
            description: '番茄钟和专注时间管理',
            component: 'timer'
        },
        {
            id: 'password',
            name: '密码管理',
            icon: '🔐',
            description: '安全的密码存储和生成',
            component: 'password'
        },
        {
            id: 'itops',
            name: 'IT运维',
            icon: '🔧',
            description: 'IT运维工具和监控',
            component: 'itops'
        },
        {
            id: 'shortcuts',
            name: '快捷工具',
            icon: '⚡',
            description: '实用的在线工具集合',
            component: 'shortcuts'
        }
    ],

    // 搜索引擎配置
    SEARCH_ENGINES: [
        {
            id: 'local',
            name: '本地搜索',
            url: '',
            placeholder: '搜索本地内容...'
        },
        {
            id: 'baidu',
            name: '百度',
            url: 'https://www.baidu.com/s?wd=',
            placeholder: '百度一下...'
        },
        {
            id: 'google',
            name: 'Google',
            url: 'https://www.google.com/search?q=',
            placeholder: 'Search Google...'
        },
        {
            id: 'bing',
            name: 'Bing',
            url: 'https://www.bing.com/search?q=',
            placeholder: 'Search Bing...'
        },
        {
            id: 'duckduckgo',
            name: 'DuckDuckGo',
            url: 'https://duckduckgo.com/?q=',
            placeholder: 'Search DuckDuckGo...'
        }
    ],

    // 便签颜色配置
    NOTE_COLORS: [
        { id: 'yellow', name: '黄色', value: '#fef3c7', text: '#92400e' },
        { id: 'blue', name: '蓝色', value: '#dbeafe', text: '#1e40af' },
        { id: 'green', name: '绿色', value: '#d1fae5', text: '#065f46' },
        { id: 'pink', name: '粉色', value: '#fce7f3', text: '#be185d' },
        { id: 'purple', name: '紫色', value: '#e9d5ff', text: '#7c2d12' },
        { id: 'orange', name: '橙色', value: '#fed7aa', text: '#9a3412' },
        { id: 'red', name: '红色', value: '#fecaca', text: '#991b1b' },
        { id: 'gray', name: '灰色', value: '#f3f4f6', text: '#374151' }
    ],

    // 任务优先级配置
    TODO_PRIORITIES: [
        { id: 'low', name: '低优先级', color: '#10b981', icon: '🔽' },
        { id: 'medium', name: '中优先级', color: '#f59e0b', icon: '➡️' },
        { id: 'high', name: '高优先级', color: '#ef4444', icon: '🔺' },
        { id: 'urgent', name: '紧急', color: '#dc2626', icon: '🚨' }
    ],

    // 任务状态配置
    TODO_STATUSES: [
        { id: 'pending', name: '待完成', color: '#6b7280', icon: '⏳' },
        { id: 'in-progress', name: '进行中', color: '#3b82f6', icon: '🔄' },
        { id: 'completed', name: '已完成', color: '#10b981', icon: '✅' },
        { id: 'overdue', name: '已逾期', color: '#ef4444', icon: '⚠️' },
        { id: 'cancelled', name: '已取消', color: '#6b7280', icon: '❌' }
    ],

    // 任务分类配置
    TODO_CATEGORIES: [
        { id: 'work', name: '工作', icon: '💼', color: '#3b82f6' },
        { id: 'personal', name: '个人', icon: '👤', color: '#10b981' },
        { id: 'study', name: '学习', icon: '📚', color: '#8b5cf6' },
        { id: 'health', name: '健康', icon: '🏃', color: '#ef4444' },
        { id: 'finance', name: '财务', icon: '💰', color: '#f59e0b' },
        { id: 'family', name: '家庭', icon: '👨‍👩‍👧‍👦', color: '#ec4899' },
        { id: 'hobby', name: '爱好', icon: '🎨', color: '#06b6d4' },
        { id: 'travel', name: '旅行', icon: '✈️', color: '#84cc16' }
    ],

    // 密码强度配置
    PASSWORD_STRENGTH: {
        weak: { score: 0, label: '弱', color: '#ef4444' },
        fair: { score: 1, label: '一般', color: '#f59e0b' },
        good: { score: 2, label: '良好', color: '#10b981' },
        strong: { score: 3, label: '强', color: '#059669' }
    },

    // 网络延迟等级
    LATENCY_LEVELS: [
        { id: 'excellent', name: '优秀', range: [0, 50], color: '#10b981' },
        { id: 'good', name: '良好', range: [51, 100], color: '#84cc16' },
        { id: 'fair', name: '一般', range: [101, 200], color: '#f59e0b' },
        { id: 'poor', name: '较慢', range: [201, 500], color: '#ef4444' },
        { id: 'offline', name: '离线', range: [501, Infinity], color: '#6b7280' }
    ],

    // 通知类型配置
    NOTIFICATION_TYPES: [
        { id: 'success', name: '成功', icon: '✅', color: '#10b981' },
        { id: 'error', name: '错误', icon: '❌', color: '#ef4444' },
        { id: 'warning', name: '警告', icon: '⚠️', color: '#f59e0b' },
        { id: 'info', name: '信息', icon: 'ℹ️', color: '#3b82f6' }
    ],

    // 文件类型配置
    FILE_TYPES: {
        image: {
            extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'],
            icon: '🖼️',
            color: '#10b981'
        },
        document: {
            extensions: ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            icon: '📄',
            color: '#3b82f6'
        },
        spreadsheet: {
            extensions: ['xls', 'xlsx', 'csv'],
            icon: '📊',
            color: '#10b981'
        },
        presentation: {
            extensions: ['ppt', 'pptx'],
            icon: '📽️',
            color: '#f59e0b'
        },
        archive: {
            extensions: ['zip', 'rar', '7z', 'tar', 'gz'],
            icon: '📦',
            color: '#6b7280'
        },
        code: {
            extensions: ['js', 'html', 'css', 'php', 'py', 'java', 'cpp'],
            icon: '💻',
            color: '#8b5cf6'
        }
    },

    // 快捷工具配置
    SHORTCUT_TOOLS: [
        {
            id: 'qr-generator',
            name: 'QR码生成器',
            icon: '📱',
            description: '生成QR二维码'
        },
        {
            id: 'json-formatter',
            name: 'JSON格式化',
            icon: '📋',
            description: '格式化和验证JSON'
        },
        {
            id: 'base64-encoder',
            name: 'Base64编码',
            icon: '🔐',
            description: 'Base64编码解码'
        },
        {
            id: 'url-encoder',
            name: 'URL编码',
            icon: '🔗',
            description: 'URL编码解码'
        },
        {
            id: 'timestamp-converter',
            name: '时间戳转换',
            icon: '⏰',
            description: '时间戳格式转换'
        },
        {
            id: 'color-picker',
            name: '颜色选择器',
            icon: '🎨',
            description: '颜色选择和转换'
        },
        {
            id: 'hash-generator',
            name: '哈希生成器',
            icon: '#️⃣',
            description: '生成MD5/SHA哈希'
        },
        {
            id: 'uuid-generator',
            name: 'UUID生成器',
            icon: '🆔',
            description: '生成唯一标识符'
        }
    ],

    // 默认设置
    DEFAULT_SETTINGS: {
        theme: 'default',
        darkMode: null, // null表示跟随系统
        searchEngine: 'local',
        autoSave: true,
        notifications: true,
        animations: true,
        compactMode: false,
        language: 'zh-CN'
    },

    // API配置
    API_CONFIG: {
        timeout: 5000,
        retryCount: 3,
        retryDelay: 1000
    },

    // 存储配置
    STORAGE_CONFIG: {
        prefix: 'nav_center_',
        version: '2.0.0',
        maxSize: 5 * 1024 * 1024, // 5MB
        compression: true
    },

    // 性能配置
    PERFORMANCE_CONFIG: {
        debounceDelay: 300,
        throttleDelay: 100,
        lazyLoadThreshold: 100,
        maxConcurrentRequests: 5
    },

    // 安全配置
    SECURITY_CONFIG: {
        passwordMinLength: 8,
        passwordMaxLength: 128,
        sessionTimeout: 30 * 60 * 1000, // 30分钟
        maxLoginAttempts: 5
    }
};

// 冻结配置对象，防止意外修改
Object.freeze(window.APP_CONFIG);

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.APP_CONFIG;
}
