// 本地存储工具类
class StorageManager {
    constructor() {
        this.prefix = 'nav_center_';
    }

    // 获取完整的键名
    getKey(key) {
        return this.prefix + key;
    }

    // 保存数据
    set(key, value) {
        try {
            const serializedValue = JSON.stringify(value);
            localStorage.setItem(this.getKey(key), serializedValue);
            return true;
        } catch (error) {
            console.error('保存数据失败:', error);
            return false;
        }
    }

    // 获取数据
    get(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(this.getKey(key));
            if (item === null) {
                return defaultValue;
            }
            return JSON.parse(item);
        } catch (error) {
            console.error('读取数据失败:', error);
            return defaultValue;
        }
    }

    // 删除数据
    remove(key) {
        try {
            localStorage.removeItem(this.getKey(key));
            return true;
        } catch (error) {
            console.error('删除数据失败:', error);
            return false;
        }
    }

    // 清空所有数据
    clear() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }

    // 获取所有键
    getAllKeys() {
        try {
            const keys = Object.keys(localStorage);
            return keys
                .filter(key => key.startsWith(this.prefix))
                .map(key => key.replace(this.prefix, ''));
        } catch (error) {
            console.error('获取键列表失败:', error);
            return [];
        }
    }

    // 获取存储大小
    getSize() {
        try {
            let total = 0;
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    total += localStorage.getItem(key).length;
                }
            });
            return total;
        } catch (error) {
            console.error('计算存储大小失败:', error);
            return 0;
        }
    }

    // 导出数据
    export() {
        try {
            const data = {};
            const keys = this.getAllKeys();
            keys.forEach(key => {
                data[key] = this.get(key);
            });
            return data;
        } catch (error) {
            console.error('导出数据失败:', error);
            return {};
        }
    }

    // 导入数据
    import(data) {
        try {
            Object.keys(data).forEach(key => {
                this.set(key, data[key]);
            });
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }

    // 备份数据到文件
    backup() {
        try {
            const data = this.export();
            const blob = new Blob([JSON.stringify(data, null, 2)], {
                type: 'application/json'
            });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `nav_center_backup_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            return true;
        } catch (error) {
            console.error('备份数据失败:', error);
            return false;
        }
    }

    // 从文件恢复数据
    restore(file) {
        return new Promise((resolve, reject) => {
            try {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target.result);
                        const success = this.import(data);
                        if (success) {
                            resolve(data);
                        } else {
                            reject(new Error('导入数据失败'));
                        }
                    } catch (parseError) {
                        reject(new Error('文件格式错误'));
                    }
                };
                reader.onerror = () => {
                    reject(new Error('文件读取失败'));
                };
                reader.readAsText(file);
            } catch (error) {
                reject(error);
            }
        });
    }

    // 数据迁移
    migrate(version) {
        try {
            const currentVersion = this.get('version', '1.0.0');
            if (currentVersion === version) {
                return true;
            }

            // 执行迁移逻辑
            switch (version) {
                case '2.0.0':
                    this.migrateToV2();
                    break;
                default:
                    console.warn('未知的版本:', version);
                    return false;
            }

            this.set('version', version);
            return true;
        } catch (error) {
            console.error('数据迁移失败:', error);
            return false;
        }
    }

    // 迁移到v2.0.0
    migrateToV2() {
        // 示例迁移逻辑
        const sites = this.get('sites', []);
        if (sites.length > 0) {
            // 为旧数据添加新字段
            const updatedSites = sites.map(site => ({
                ...site,
                category: site.category || 'default',
                tags: site.tags || [],
                createdAt: site.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }));
            this.set('sites', updatedSites);
        }
    }

    // 检查存储配额
    checkQuota() {
        try {
            if ('storage' in navigator && 'estimate' in navigator.storage) {
                return navigator.storage.estimate().then(estimate => {
                    const used = estimate.usage || 0;
                    const quota = estimate.quota || 0;
                    const percentage = quota > 0 ? (used / quota) * 100 : 0;
                    
                    return {
                        used: used,
                        quota: quota,
                        percentage: percentage,
                        available: quota - used
                    };
                });
            } else {
                // 降级方案
                const used = this.getSize();
                return Promise.resolve({
                    used: used,
                    quota: 5 * 1024 * 1024, // 假设5MB配额
                    percentage: (used / (5 * 1024 * 1024)) * 100,
                    available: (5 * 1024 * 1024) - used
                });
            }
        } catch (error) {
            console.error('检查存储配额失败:', error);
            return Promise.resolve({
                used: 0,
                quota: 0,
                percentage: 0,
                available: 0
            });
        }
    }

    // 压缩数据
    compress(data) {
        try {
            // 简单的压缩：移除不必要的空格和换行
            return JSON.stringify(data);
        } catch (error) {
            console.error('压缩数据失败:', error);
            return JSON.stringify(data);
        }
    }

    // 解压数据
    decompress(compressedData) {
        try {
            return JSON.parse(compressedData);
        } catch (error) {
            console.error('解压数据失败:', error);
            return null;
        }
    }
}

// 创建全局实例
window.storage = new StorageManager();

// 导出类和实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StorageManager, storage: window.storage };
}
