/* 主题样式文件 */

/* 默认主题（浅色） */
.theme-default {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-card: rgba(255, 255, 255, 0.8);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --accent-color: #3b82f6;
    --accent-hover: #2563eb;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;
}

/* 深色主题 */
.theme-dark {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-card: rgba(31, 41, 55, 0.8);
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --accent-color: #60a5fa;
    --accent-hover: #3b82f6;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    --info-color: #60a5fa;
}

/* 蓝色主题 */
.theme-blue {
    --bg-primary: #f0f9ff;
    --bg-secondary: #e0f2fe;
    --bg-card: rgba(240, 249, 255, 0.8);
    --text-primary: #0c4a6e;
    --text-secondary: #0369a1;
    --text-muted: #0284c7;
    --border-color: #bae6fd;
    --accent-color: #0ea5e9;
    --accent-hover: #0284c7;
    --shadow-color: rgba(14, 165, 233, 0.1);
    --success-color: #059669;
    --warning-color: #d97706;
    --error-color: #dc2626;
    --info-color: #0ea5e9;
}

/* 绿色主题 */
.theme-green {
    --bg-primary: #f0fdf4;
    --bg-secondary: #dcfce7;
    --bg-card: rgba(240, 253, 244, 0.8);
    --text-primary: #14532d;
    --text-secondary: #166534;
    --text-muted: #15803d;
    --border-color: #bbf7d0;
    --accent-color: #22c55e;
    --accent-hover: #16a34a;
    --shadow-color: rgba(34, 197, 94, 0.1);
    --success-color: #22c55e;
    --warning-color: #eab308;
    --error-color: #ef4444;
    --info-color: #3b82f6;
}

/* 紫色主题 */
.theme-purple {
    --bg-primary: #faf5ff;
    --bg-secondary: #f3e8ff;
    --bg-card: rgba(250, 245, 255, 0.8);
    --text-primary: #581c87;
    --text-secondary: #7c3aed;
    --text-muted: #8b5cf6;
    --border-color: #d8b4fe;
    --accent-color: #a855f7;
    --accent-hover: #9333ea;
    --shadow-color: rgba(168, 85, 247, 0.1);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #a855f7;
}

/* 橙色主题 */
.theme-orange {
    --bg-primary: #fffbeb;
    --bg-secondary: #fef3c7;
    --bg-card: rgba(255, 251, 235, 0.8);
    --text-primary: #92400e;
    --text-secondary: #d97706;
    --text-muted: #f59e0b;
    --border-color: #fed7aa;
    --accent-color: #f97316;
    --accent-hover: #ea580c;
    --shadow-color: rgba(249, 115, 22, 0.1);
    --success-color: #10b981;
    --warning-color: #f97316;
    --error-color: #ef4444;
    --info-color: #3b82f6;
}

/* 粉色主题 */
.theme-pink {
    --bg-primary: #fdf2f8;
    --bg-secondary: #fce7f3;
    --bg-card: rgba(253, 242, 248, 0.8);
    --text-primary: #831843;
    --text-secondary: #be185d;
    --text-muted: #ec4899;
    --border-color: #f9a8d4;
    --accent-color: #ec4899;
    --accent-hover: #db2777;
    --shadow-color: rgba(236, 72, 153, 0.1);
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #ec4899;
}

/* 主题切换动画 */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 深色模式特殊处理 */
.dark {
    color-scheme: dark;
}

.dark .theme-default {
    --bg-primary: #111827;
    --bg-secondary: #1f2937;
    --bg-card: rgba(31, 41, 55, 0.8);
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --border-color: #374151;
    --accent-color: #60a5fa;
    --accent-hover: #3b82f6;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

/* 主题特定的组件样式 */
.theme-blue .modern-card:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px -3px rgba(14, 165, 233, 0.2);
}

.theme-green .modern-card:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px -3px rgba(34, 197, 94, 0.2);
}

.theme-purple .modern-card:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px -3px rgba(168, 85, 247, 0.2);
}

.theme-orange .modern-card:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px -3px rgba(249, 115, 22, 0.2);
}

.theme-pink .modern-card:hover {
    border-color: var(--accent-color);
    box-shadow: 0 10px 25px -3px rgba(236, 72, 153, 0.2);
}

/* 主题选择器样式 */
.theme-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    padding: 1rem;
}

.theme-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border: 2px solid transparent;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-option.active {
    border-color: var(--accent-color);
    background: var(--bg-secondary);
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    position: relative;
    overflow: hidden;
}

.theme-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: var(--bg-primary);
}

.theme-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: var(--accent-color);
}

/* 响应式主题调整 */
@media (max-width: 768px) {
    .theme-selector {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .theme-option {
        padding: 0.75rem;
    }
    
    .theme-preview {
        width: 50px;
        height: 30px;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-primary: #000000;
        --bg-primary: #ffffff;
    }
    
    .dark {
        --border-color: #ffffff;
        --text-primary: #ffffff;
        --bg-primary: #000000;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}
