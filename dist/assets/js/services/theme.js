// 主题服务
class ThemeService {
    constructor() {
        this.currentTheme = 'default';
        this.isDarkMode = false;
        this.themes = {
            default: {
                name: '默认',
                colors: {
                    primary: '#3b82f6',
                    secondary: '#6b7280',
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444'
                }
            },
            blue: {
                name: '蓝色',
                colors: {
                    primary: '#0ea5e9',
                    secondary: '#0284c7',
                    success: '#059669',
                    warning: '#d97706',
                    error: '#dc2626'
                }
            },
            green: {
                name: '绿色',
                colors: {
                    primary: '#22c55e',
                    secondary: '#16a34a',
                    success: '#22c55e',
                    warning: '#eab308',
                    error: '#ef4444'
                }
            },
            purple: {
                name: '紫色',
                colors: {
                    primary: '#a855f7',
                    secondary: '#9333ea',
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444'
                }
            },
            orange: {
                name: '橙色',
                colors: {
                    primary: '#f97316',
                    secondary: '#ea580c',
                    success: '#10b981',
                    warning: '#f97316',
                    error: '#ef4444'
                }
            },
            pink: {
                name: '粉色',
                colors: {
                    primary: '#ec4899',
                    secondary: '#db2777',
                    success: '#10b981',
                    warning: '#f59e0b',
                    error: '#ef4444'
                }
            }
        };
        this.init();
    }

    // 初始化主题
    init() {
        this.loadTheme();
        this.setupMediaQuery();
        this.applyTheme();
    }

    // 加载保存的主题设置
    loadTheme() {
        const savedTheme = storage.get('theme', 'default');
        const savedDarkMode = storage.get('darkMode', null);
        
        this.currentTheme = savedTheme;
        
        if (savedDarkMode !== null) {
            this.isDarkMode = savedDarkMode;
        } else {
            // 检查系统偏好
            this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        }
    }

    // 设置媒体查询监听
    setupMediaQuery() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            // 只有在用户没有手动设置时才跟随系统
            if (storage.get('darkMode') === null) {
                this.isDarkMode = e.matches;
                this.applyTheme();
                this.emit('change', {
                    theme: this.currentTheme,
                    darkMode: this.isDarkMode,
                    auto: true
                });
            }
        });
    }

    // 应用主题
    applyTheme() {
        const body = document.body;
        const html = document.documentElement;
        
        // 移除所有主题类
        Object.keys(this.themes).forEach(theme => {
            body.classList.remove(`theme-${theme}`);
        });
        
        // 应用当前主题
        body.classList.add(`theme-${this.currentTheme}`);
        
        // 应用深色模式
        if (this.isDarkMode) {
            body.classList.add('dark');
            html.classList.add('dark');
        } else {
            body.classList.remove('dark');
            html.classList.remove('dark');
        }

        // 更新CSS变量
        this.updateCSSVariables();
        
        // 更新meta标签
        this.updateMetaTheme();
    }

    // 更新CSS变量
    updateCSSVariables() {
        const theme = this.themes[this.currentTheme];
        if (!theme) return;

        const root = document.documentElement;
        Object.entries(theme.colors).forEach(([key, value]) => {
            root.style.setProperty(`--color-${key}`, value);
        });
    }

    // 更新meta主题色
    updateMetaTheme() {
        let metaTheme = document.querySelector('meta[name="theme-color"]');
        if (!metaTheme) {
            metaTheme = document.createElement('meta');
            metaTheme.name = 'theme-color';
            document.head.appendChild(metaTheme);
        }
        
        const theme = this.themes[this.currentTheme];
        metaTheme.content = theme ? theme.colors.primary : '#3b82f6';
    }

    // 设置主题
    setTheme(themeName) {
        if (!this.themes[themeName]) {
            console.warn(`主题 "${themeName}" 不存在`);
            return false;
        }

        this.currentTheme = themeName;
        storage.set('theme', themeName);
        this.applyTheme();
        
        this.emit('change', {
            theme: this.currentTheme,
            darkMode: this.isDarkMode
        });
        
        return true;
    }

    // 切换深色模式
    toggleDarkMode() {
        this.isDarkMode = !this.isDarkMode;
        storage.set('darkMode', this.isDarkMode);
        this.applyTheme();
        
        this.emit('change', {
            theme: this.currentTheme,
            darkMode: this.isDarkMode
        });
    }

    // 设置深色模式
    setDarkMode(enabled) {
        this.isDarkMode = enabled;
        storage.set('darkMode', enabled);
        this.applyTheme();
        
        this.emit('change', {
            theme: this.currentTheme,
            darkMode: this.isDarkMode
        });
    }

    // 重置为系统偏好
    resetToSystem() {
        storage.remove('darkMode');
        this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.applyTheme();
        
        this.emit('change', {
            theme: this.currentTheme,
            darkMode: this.isDarkMode,
            auto: true
        });
    }

    // 获取当前主题信息
    getCurrentTheme() {
        return {
            name: this.currentTheme,
            isDarkMode: this.isDarkMode,
            theme: this.themes[this.currentTheme]
        };
    }

    // 获取所有主题
    getAllThemes() {
        return this.themes;
    }

    // 添加自定义主题
    addTheme(name, theme) {
        this.themes[name] = theme;
        this.emit('themeAdded', { name, theme });
    }

    // 移除主题
    removeTheme(name) {
        if (name === 'default') {
            console.warn('不能删除默认主题');
            return false;
        }
        
        if (this.currentTheme === name) {
            this.setTheme('default');
        }
        
        delete this.themes[name];
        this.emit('themeRemoved', { name });
        return true;
    }

    // 导出主题配置
    exportTheme(name = null) {
        if (name) {
            return this.themes[name] || null;
        }
        return {
            currentTheme: this.currentTheme,
            isDarkMode: this.isDarkMode,
            themes: this.themes
        };
    }

    // 导入主题配置
    importTheme(config) {
        try {
            if (config.themes) {
                Object.assign(this.themes, config.themes);
            }
            
            if (config.currentTheme && this.themes[config.currentTheme]) {
                this.currentTheme = config.currentTheme;
            }
            
            if (typeof config.isDarkMode === 'boolean') {
                this.isDarkMode = config.isDarkMode;
            }
            
            this.applyTheme();
            this.saveSettings();
            
            this.emit('imported', config);
            return true;
        } catch (error) {
            console.error('导入主题失败:', error);
            return false;
        }
    }

    // 保存设置
    saveSettings() {
        storage.set('theme', this.currentTheme);
        storage.set('darkMode', this.isDarkMode);
    }

    // 获取主题预览
    getThemePreview(themeName) {
        const theme = this.themes[themeName];
        if (!theme) return null;
        
        return {
            name: theme.name,
            colors: theme.colors,
            preview: this.generatePreviewCSS(theme)
        };
    }

    // 生成预览CSS
    generatePreviewCSS(theme) {
        return `
            .theme-preview-${theme.name} {
                --color-primary: ${theme.colors.primary};
                --color-secondary: ${theme.colors.secondary};
                --color-success: ${theme.colors.success};
                --color-warning: ${theme.colors.warning};
                --color-error: ${theme.colors.error};
            }
        `;
    }

    // 事件系统
    emit(event, data) {
        const customEvent = new CustomEvent(`theme:${event}`, {
            detail: data
        });
        document.dispatchEvent(customEvent);
    }

    // 监听事件
    on(event, handler) {
        document.addEventListener(`theme:${event}`, handler);
    }

    // 移除事件监听
    off(event, handler) {
        document.removeEventListener(`theme:${event}`, handler);
    }

    // 获取系统偏好
    getSystemPreference() {
        return {
            darkMode: window.matchMedia('(prefers-color-scheme: dark)').matches,
            reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
            highContrast: window.matchMedia('(prefers-contrast: high)').matches
        };
    }

    // 应用无障碍设置
    applyAccessibilitySettings() {
        const prefs = this.getSystemPreference();
        const body = document.body;
        
        if (prefs.reducedMotion) {
            body.classList.add('reduce-motion');
        } else {
            body.classList.remove('reduce-motion');
        }
        
        if (prefs.highContrast) {
            body.classList.add('high-contrast');
        } else {
            body.classList.remove('high-contrast');
        }
    }
}

// 创建全局实例
window.themeService = new ThemeService();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ThemeService, themeService: window.themeService };
}
