// 便签搜索服务
class NotesSearchService {
    constructor() {
        this.searchHistory = this.loadSearchHistory();
        this.searchIndex = new Map(); // 搜索索引
        this.stopWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这']);
    }

    // 构建搜索索引
    buildSearchIndex(notes) {
        this.searchIndex.clear();

        notes.forEach(note => {
            const searchableText = this.extractSearchableText(note);
            const keywords = this.extractKeywords(searchableText);

            keywords.forEach(keyword => {
                if (!this.searchIndex.has(keyword)) {
                    this.searchIndex.set(keyword, new Set());
                }
                this.searchIndex.get(keyword).add(note.id);
            });
        });
    }

    // 提取可搜索的文本
    extractSearchableText(note) {
        let text = '';

        // 标题
        if (note.title) {
            text += note.title + ' ';
        }

        // 内容（移除HTML标签）
        if (note.content) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = note.content;
            text += tempDiv.textContent || tempDiv.innerText || '';
        }

        // 标签
        if (note.tags && Array.isArray(note.tags)) {
            text += ' ' + note.tags.join(' ');
        }

        // 分类
        if (note.category) {
            text += ' ' + note.category;
        }

        return text.toLowerCase();
    }

    // 提取关键词
    extractKeywords(text) {
        const keywords = new Set();

        // 分词（简单实现）
        const words = text.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g) || [];

        words.forEach(word => {
            if (word.length >= 2 && !this.stopWords.has(word)) {
                keywords.add(word);

                // 添加子字符串（用于模糊匹配）
                if (word.length > 2) {
                    for (let i = 0; i <= word.length - 2; i++) {
                        for (let j = i + 2; j <= word.length; j++) {
                            keywords.add(word.substring(i, j));
                        }
                    }
                }
            }
        });

        return Array.from(keywords);
    }

    // 执行搜索
    search(query, notes, options = {}) {
        const {
            fuzzy = true,
            maxResults = 50,
            sortBy = 'relevance' // 'relevance', 'date', 'title'
        } = options;

        if (!query || query.trim().length === 0) {
            return notes;
        }

        const searchQuery = query.toLowerCase().trim();
        this.addToSearchHistory(searchQuery);

        // 精确匹配
        let exactMatches = this.exactSearch(searchQuery, notes);

        // 模糊匹配
        let fuzzyMatches = [];
        if (fuzzy && exactMatches.length < maxResults) {
            fuzzyMatches = this.fuzzySearch(searchQuery, notes, exactMatches);
        }

        // 合并结果
        let results = [...exactMatches, ...fuzzyMatches];

        // 去重
        const uniqueResults = Array.from(new Map(results.map(item => [item.note.id, item])).values());

        // 排序
        uniqueResults.sort((a, b) => {
            switch (sortBy) {
                case 'date':
                    return new Date(b.note.updatedAt || b.note.createdAt) - new Date(a.note.updatedAt || a.note.createdAt);
                case 'title':
                    return (a.note.title || '').localeCompare(b.note.title || '');
                case 'relevance':
                default:
                    return b.score - a.score;
            }
        });

        return uniqueResults.slice(0, maxResults).map(item => ({
            ...item.note,
            searchScore: item.score,
            matchedText: item.matchedText
        }));
    }

    // 精确搜索
    exactSearch(query, notes) {
        const results = [];

        notes.forEach(note => {
            const searchableText = this.extractSearchableText(note);
            const score = this.calculateExactScore(query, searchableText, note);

            if (score > 0) {
                results.push({
                    note,
                    score,
                    matchedText: this.extractMatchedText(query, searchableText)
                });
            }
        });

        return results;
    }

    // 模糊搜索
    fuzzySearch(query, notes, excludeNotes = []) {
        const excludeIds = new Set(excludeNotes.map(item => item.note.id));
        const results = [];

        notes.forEach(note => {
            if (excludeIds.has(note.id)) return;

            const searchableText = this.extractSearchableText(note);
            const score = this.calculateFuzzyScore(query, searchableText, note);

            if (score > 0.3) { // 模糊匹配阈值
                results.push({
                    note,
                    score,
                    matchedText: this.extractMatchedText(query, searchableText)
                });
            }
        });

        return results;
    }

    // 计算精确匹配分数
    calculateExactScore(query, text, note) {
        let score = 0;

        // 标题匹配权重更高
        const title = (note.title || '').toLowerCase();
        if (title.includes(query)) {
            score += title === query ? 100 : 50;
        }

        // 内容匹配
        if (text.includes(query)) {
            score += 20;

            // 计算匹配次数
            const matches = text.split(query).length - 1;
            score += matches * 5;
        }

        // 标签匹配
        if (note.tags && note.tags.some(tag => tag.toLowerCase().includes(query))) {
            score += 30;
        }

        return score;
    }

    // 计算模糊匹配分数
    calculateFuzzyScore(query, text, note) {
        const queryWords = query.split(/\s+/);
        let score = 0;
        let matchedWords = 0;

        queryWords.forEach(word => {
            if (word.length < 2) return;

            // 检查是否包含该词
            if (text.includes(word)) {
                matchedWords++;
                score += 10;
            } else {
                // 计算编辑距离
                const similarity = this.calculateSimilarity(word, text);
                if (similarity > 0.6) {
                    matchedWords++;
                    score += similarity * 5;
                }
            }
        });

        // 匹配词汇比例
        const matchRatio = matchedWords / queryWords.length;
        score *= matchRatio;

        return score;
    }

    // 计算字符串相似度
    calculateSimilarity(word, text) {
        let maxSimilarity = 0;
        const words = text.split(/\s+/);

        words.forEach(textWord => {
            if (textWord.length >= 2) {
                const similarity = this.levenshteinSimilarity(word, textWord);
                maxSimilarity = Math.max(maxSimilarity, similarity);
            }
        });

        return maxSimilarity;
    }

    // 计算编辑距离相似度
    levenshteinSimilarity(str1, str2) {
        const len1 = str1.length;
        const len2 = str2.length;

        if (len1 === 0) return len2 === 0 ? 1 : 0;
        if (len2 === 0) return 0;

        const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));

        for (let i = 0; i <= len1; i++) matrix[i][0] = i;
        for (let j = 0; j <= len2; j++) matrix[0][j] = j;

        for (let i = 1; i <= len1; i++) {
            for (let j = 1; j <= len2; j++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[i][j] = Math.min(
                    matrix[i - 1][j] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j - 1] + cost
                );
            }
        }

        const distance = matrix[len1][len2];
        const maxLen = Math.max(len1, len2);
        return 1 - distance / maxLen;
    }

    // 提取匹配的文本片段
    extractMatchedText(query, text) {
        const index = text.indexOf(query.toLowerCase());
        if (index === -1) return '';

        const start = Math.max(0, index - 20);
        const end = Math.min(text.length, index + query.length + 20);

        let excerpt = text.substring(start, end);
        if (start > 0) excerpt = '...' + excerpt;
        if (end < text.length) excerpt = excerpt + '...';

        return excerpt;
    }

    // 高亮搜索结果
    highlightSearchResults(text, query) {
        if (!query || !text) return text;

        const regex = new RegExp(`(${this.escapeRegExp(query)})`, 'gi');
        return text.replace(regex, '<mark class="search-highlight">$1</mark>');
    }

    // 转义正则表达式特殊字符
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // 获取搜索建议
    getSearchSuggestions(query, notes, maxSuggestions = 5) {
        if (!query || query.length < 2) {
            return this.getRecentSearches(maxSuggestions);
        }

        const suggestions = new Set();
        const queryLower = query.toLowerCase();

        // 从便签标题中提取建议
        notes.forEach(note => {
            if (note.title) {
                const title = note.title.toLowerCase();
                if (title.includes(queryLower)) {
                    suggestions.add(note.title);
                }
            }

            // 从标签中提取建议
            if (note.tags) {
                note.tags.forEach(tag => {
                    if (tag.toLowerCase().includes(queryLower)) {
                        suggestions.add(tag);
                    }
                });
            }
        });

        return Array.from(suggestions).slice(0, maxSuggestions);
    }

    // 添加到搜索历史
    addToSearchHistory(query) {
        if (!query || query.length < 2) return;

        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);

        // 添加到开头
        this.searchHistory.unshift({
            query,
            timestamp: Date.now()
        });

        // 限制历史记录数量
        this.searchHistory = this.searchHistory.slice(0, 20);

        this.saveSearchHistory();
    }

    // 获取最近搜索
    getRecentSearches(limit = 5) {
        return this.searchHistory.slice(0, limit).map(item => item.query);
    }

    // 清空搜索历史
    clearSearchHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
    }

    // 保存搜索历史
    saveSearchHistory() {
        try {
            localStorage.setItem('notes_search_history', JSON.stringify(this.searchHistory));
        } catch (e) {
            console.warn('无法保存搜索历史:', e);
        }
    }

    // 加载搜索历史
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('notes_search_history');
            return history ? JSON.parse(history) : [];
        } catch (e) {
            console.warn('无法加载搜索历史:', e);
            return [];
        }
    }

    // 高级搜索
    advancedSearch(notes, options = {}) {
        const {
            query = '',
            category = '',
            color = '',
            tags = [],
            dateRange = null,
            isPinned = null,
            hasImages = null,
            sortBy = 'relevance',
            sortOrder = 'desc'
        } = options;

        let results = [...notes];

        // 文本搜索
        if (query) {
            results = this.search(results, query);
        }

        // 分类过滤
        if (category) {
            results = results.filter(note => note.category === category);
        }

        // 颜色过滤
        if (color) {
            results = results.filter(note => note.color === color);
        }

        // 标签过滤
        if (tags.length > 0) {
            results = results.filter(note => {
                if (!note.tags || !Array.isArray(note.tags)) return false;
                return tags.every(tag => note.tags.includes(tag));
            });
        }

        // 置顶过滤
        if (isPinned !== null) {
            results = results.filter(note => Boolean(note.isPinned) === isPinned);
        }

        // 图片过滤
        if (hasImages !== null) {
            results = results.filter(note => {
                const hasImg = note.content && note.content.includes('<img');
                return hasImg === hasImages;
            });
        }

        // 日期范围过滤
        if (dateRange && dateRange.start && dateRange.end) {
            const startDate = new Date(dateRange.start);
            const endDate = new Date(dateRange.end);
            endDate.setHours(23, 59, 59, 999);

            results = results.filter(note => {
                const noteDate = new Date(note.createdAt || note.updatedAt);
                return noteDate >= startDate && noteDate <= endDate;
            });
        }

        // 排序
        results = this.sortResults(results, sortBy, sortOrder);

        return results;
    }

    // 排序结果
    sortResults(results, sortBy, sortOrder = 'desc') {
        const sortFunctions = {
            relevance: (a, b) => {
                if (a.isPinned && !b.isPinned) return -1;
                if (!a.isPinned && b.isPinned) return 1;

                const aTime = new Date(a.updatedAt || a.createdAt);
                const bTime = new Date(b.updatedAt || b.createdAt);
                return bTime - aTime;
            },

            created: (a, b) => {
                const aTime = new Date(a.createdAt);
                const bTime = new Date(b.createdAt);
                return sortOrder === 'desc' ? bTime - aTime : aTime - bTime;
            },

            updated: (a, b) => {
                const aTime = new Date(a.updatedAt || a.createdAt);
                const bTime = new Date(b.updatedAt || b.createdAt);
                return sortOrder === 'desc' ? bTime - aTime : aTime - bTime;
            },

            title: (a, b) => {
                const aTitle = (a.title || '').toLowerCase();
                const bTitle = (b.title || '').toLowerCase();
                const result = aTitle.localeCompare(bTitle);
                return sortOrder === 'desc' ? -result : result;
            }
        };

        const sortFn = sortFunctions[sortBy] || sortFunctions.relevance;
        return results.sort(sortFn);
    }

    // 获取热门标签
    getPopularTags(notes, limit = 10) {
        const tagCounts = {};

        notes.forEach(note => {
            if (note.tags && Array.isArray(note.tags)) {
                note.tags.forEach(tag => {
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
            }
        });

        return Object.entries(tagCounts)
            .sort((a, b) => b[1] - a[1])
            .slice(0, limit)
            .map(([tag, count]) => ({ tag, count }));
    }

    // 获取搜索统计
    getSearchStats(notes, searchResults) {
        const stats = {
            total: notes.length,
            found: searchResults.length,
            categories: {},
            colors: {},
            tags: {},
            withImages: 0,
            pinned: 0
        };

        searchResults.forEach(note => {
            const category = note.category || 'uncategorized';
            stats.categories[category] = (stats.categories[category] || 0) + 1;

            const color = note.color || 'default';
            stats.colors[color] = (stats.colors[color] || 0) + 1;

            if (note.tags && Array.isArray(note.tags)) {
                note.tags.forEach(tag => {
                    stats.tags[tag] = (stats.tags[tag] || 0) + 1;
                });
            }

            if (note.content && note.content.includes('<img')) {
                stats.withImages++;
            }

            if (note.isPinned) {
                stats.pinned++;
            }
        });

        return stats;
    }

    // 导出搜索结果
    exportSearchResults(results, format = 'json') {
        const exportData = {
            exportDate: new Date().toISOString(),
            totalResults: results.length,
            results: results.map(note => ({
                id: note.id,
                title: note.title,
                content: this.stripHtml(note.content || ''),
                category: note.category,
                color: note.color,
                tags: note.tags,
                isPinned: note.isPinned,
                createdAt: note.createdAt,
                updatedAt: note.updatedAt
            }))
        };

        if (format === 'json') {
            return JSON.stringify(exportData, null, 2);
        } else if (format === 'csv') {
            return this.convertToCSV(exportData.results);
        }

        return exportData;
    }

    // 转换为CSV格式
    convertToCSV(data) {
        if (!data.length) return '';

        const headers = ['标题', '内容', '分类', '颜色', '标签', '置顶', '创建时间', '更新时间'];
        const rows = data.map(note => [
            note.title || '',
            note.content || '',
            note.category || '',
            note.color || '',
            (note.tags || []).join(';'),
            note.isPinned ? '是' : '否',
            note.createdAt || '',
            note.updatedAt || ''
        ]);

        return [headers, ...rows]
            .map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
            .join('\n');
    }

    // 移除HTML标签
    stripHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }
}

// 导出到全局
window.NotesSearchService = NotesSearchService;
