// 富文本编辑器服务
class RichTextEditor {
    constructor(container, options = {}) {
        this.container = container;
        this.options = {
            placeholder: '开始输入...',
            minHeight: 200,
            maxHeight: 400,
            autoSave: true,
            autoSaveDelay: 1000,
            ...options
        };

        this.editor = null;
        this.toolbar = null;
        this.content = '';
        this.autoSaveTimer = null;
        this.isLinkModalOpen = false;

        this.init();
    }

    init() {
        this.createEditor();
        this.bindEvents();
        if (this.options.autoSave) {
            this.setupAutoSave();
        }
    }

    createEditor() {
        this.container.innerHTML = `
            <div class="rich-text-editor">
                <div class="editor-toolbar">
                    <div class="toolbar-group">
                        <button class="toolbar-button" data-command="bold" title="粗体">
                            <strong>B</strong>
                        </button>
                        <button class="toolbar-button" data-command="italic" title="斜体">
                            <em>I</em>
                        </button>
                        <button class="toolbar-button" data-command="underline" title="下划线">
                            <u>U</u>
                        </button>
                        <button class="toolbar-button" data-command="strikeThrough" title="删除线">
                            <s>S</s>
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <select class="toolbar-select" data-command="formatBlock" title="标题">
                            <option value="p">正文</option>
                            <option value="h1">标题 1</option>
                            <option value="h2">标题 2</option>
                            <option value="h3">标题 3</option>
                        </select>
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-command="insertUnorderedList" title="无序列表">
                            ⋅⋅⋅
                        </button>
                        <button class="toolbar-button" data-command="insertOrderedList" title="有序列表">
                            123
                        </button>
                        <button class="toolbar-button" data-command="blockquote" title="引用">
                            ❝❞
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-command="createLink" title="插入链接">
                            🔗
                        </button>
                        <button class="toolbar-button" data-command="insertImage" title="插入图片">
                            🖼️
                        </button>
                        <button class="toolbar-button" data-command="insertCode" title="代码">
                            &lt;/&gt;
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-command="foreColor" title="文字颜色">
                            🎨
                        </button>
                        <button class="toolbar-button" data-command="hiliteColor" title="背景颜色">
                            🖍️
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-command="undo" title="撤销">
                            ↶
                        </button>
                        <button class="toolbar-button" data-command="redo" title="重做">
                            ↷
                        </button>
                    </div>
                </div>

                <div
                    class="editor-content"
                    contenteditable="true"
                    data-placeholder="${this.options.placeholder}"
                    style="min-height: ${this.options.minHeight}px; max-height: ${this.options.maxHeight}px;"
                ></div>
            </div>
        `;

        this.toolbar = this.container.querySelector('.editor-toolbar');
        this.editor = this.container.querySelector('.editor-content');
    }

    bindEvents() {
        // 工具栏按钮事件
        this.toolbar.addEventListener('click', (e) => {
            const button = e.target.closest('.toolbar-button');
            if (button) {
                e.preventDefault();
                const command = button.dataset.command;
                this.executeCommand(command);
            }
        });

        // 下拉选择事件
        this.toolbar.addEventListener('change', (e) => {
            if (e.target.classList.contains('toolbar-select')) {
                const command = e.target.dataset.command;
                const value = e.target.value;
                this.executeCommand(command, value);
            }
        });

        // 编辑器内容变化事件
        this.editor.addEventListener('input', () => {
            this.updateContent();
            this.updateToolbarState();
            if (this.options.autoSave) {
                this.scheduleAutoSave();
            }
        });

        // 键盘快捷键
        this.editor.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 粘贴事件处理
        this.editor.addEventListener('paste', (e) => {
            this.handlePaste(e);
        });

        // 拖拽上传图片
        this.editor.addEventListener('dragover', (e) => {
            e.preventDefault();
        });

        this.editor.addEventListener('drop', (e) => {
            e.preventDefault();
            this.handleImageDrop(e);
        });
    }

    executeCommand(command, value = null) {
        this.editor.focus();

        switch (command) {
            case 'createLink':
                this.showLinkModal();
                break;
            case 'insertImage':
                this.showImageUpload();
                break;
            case 'insertCode':
                this.insertCodeBlock();
                break;
            case 'blockquote':
                this.toggleBlockquote();
                break;
            case 'foreColor':
            case 'hiliteColor':
                this.showColorPicker(command);
                break;
            default:
                document.execCommand(command, false, value);
                break;
        }

        this.updateToolbarState();
    }

    showLinkModal() {
        if (this.isLinkModalOpen) return;

        this.isLinkModalOpen = true;
        const selection = window.getSelection();
        const selectedText = selection.toString();

        const modal = document.createElement('div');
        modal.className = 'link-modal';
        modal.innerHTML = `
            <div class="link-modal-content">
                <h3>插入链接</h3>
                <input type="text" placeholder="链接文本" value="${selectedText}" id="linkText">
                <input type="url" placeholder="链接地址" id="linkUrl">
                <div class="link-modal-buttons">
                    <button class="btn-secondary" onclick="this.closest('.link-modal').remove(); window.richTextEditor.isLinkModalOpen = false;">取消</button>
                    <button class="btn-primary" onclick="window.richTextEditor.insertLink()">确定</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.querySelector('#linkText').focus();

        // 临时存储选择范围
        this.savedSelection = selection.getRangeAt(0);
        window.richTextEditor = this;
    }

    insertLink() {
        const modal = document.querySelector('.link-modal');
        const text = modal.querySelector('#linkText').value;
        const url = modal.querySelector('#linkUrl').value;

        if (url) {
            // 恢复选择范围
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(this.savedSelection);

            if (text) {
                document.execCommand('insertHTML', false, `<a href="${url}" target="_blank">${text}</a>`);
            } else {
                document.execCommand('createLink', false, url);
            }
        }

        modal.remove();
        this.isLinkModalOpen = false;
        this.editor.focus();
    }

    showImageUpload() {
        const modal = document.createElement('div');
        modal.className = 'image-upload-modal';
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h3>插入图片</h3>
                    <button class="modal-close" onclick="this.closest('.image-upload-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="upload-tabs">
                        <button class="tab-button active" data-tab="upload">上传图片</button>
                        <button class="tab-button" data-tab="url">网络图片</button>
                    </div>

                    <div class="tab-content" id="upload-tab">
                        <div class="upload-area" id="uploadArea">
                            <div class="upload-icon">📷</div>
                            <p>点击选择图片或拖拽图片到此处</p>
                            <p class="upload-hint">支持 JPG、PNG、GIF、WebP 格式，最大 5MB</p>
                            <input type="file" id="imageInput" accept="image/*" style="display: none;">
                        </div>
                        <div class="image-preview" id="imagePreview" style="display: none;">
                            <img id="previewImg" style="max-width: 100%; max-height: 200px;">
                            <div class="preview-actions">
                                <button class="btn-secondary" onclick="document.getElementById('imagePreview').style.display='none'; document.getElementById('uploadArea').style.display='block'">重新选择</button>
                                <button class="btn-primary" onclick="window.richTextEditor.insertSelectedImage()">插入图片</button>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="url-tab" style="display: none;">
                        <div class="form-group">
                            <label for="imageUrl">图片URL:</label>
                            <input type="url" id="imageUrl" placeholder="https://example.com/image.jpg" class="form-input">
                        </div>
                        <div class="form-group">
                            <label for="imageAlt">图片描述 (可选):</label>
                            <input type="text" id="imageAlt" placeholder="图片描述" class="form-input">
                        </div>
                        <div class="url-preview" id="urlPreview" style="display: none;">
                            <img id="urlPreviewImg" style="max-width: 100%; max-height: 200px;">
                        </div>
                        <div class="form-actions">
                            <button class="btn-secondary" onclick="window.richTextEditor.previewUrlImage()">预览</button>
                            <button class="btn-primary" onclick="window.richTextEditor.insertUrlImage()">插入图片</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        this.bindImageUploadEvents(modal);

        // 临时存储选择范围
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
            this.savedSelection = selection.getRangeAt(0);
        }
        window.richTextEditor = this;
    }

    bindImageUploadEvents(modal) {
        const uploadArea = modal.querySelector('#uploadArea');
        const imageInput = modal.querySelector('#imageInput');
        const tabButtons = modal.querySelectorAll('.tab-button');
        const imageUrl = modal.querySelector('#imageUrl');

        // 标签切换
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;

                // 更新按钮状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // 显示对应内容
                modal.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                });
                modal.querySelector(`#${tabId}-tab`).style.display = 'block';
            });
        });

        // 上传区域点击
        uploadArea.addEventListener('click', () => {
            imageInput.click();
        });

        // 文件选择
        imageInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleImageFile(file, modal);
            }
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                this.handleImageFile(files[0], modal);
            }
        });
    }

    handleImageFile(file, modal) {
        // 检查文件大小 (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('图片文件过大，请选择小于5MB的图片');
            return;
        }

        // 检查文件类型
        if (!file.type.startsWith('image/')) {
            alert('请选择有效的图片文件');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            const previewImg = modal.querySelector('#previewImg');
            previewImg.src = e.target.result;

            // 显示预览
            modal.querySelector('#uploadArea').style.display = 'none';
            modal.querySelector('#imagePreview').style.display = 'block';

            // 存储图片数据
            this.selectedImageData = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    previewUrlImage() {
        const imageUrl = document.querySelector('#imageUrl').value.trim();
        const urlPreview = document.querySelector('#urlPreview');
        const urlPreviewImg = document.querySelector('#urlPreviewImg');

        if (!imageUrl) {
            urlPreview.style.display = 'none';
            return;
        }

        urlPreviewImg.onload = () => {
            urlPreview.style.display = 'block';
        };

        urlPreviewImg.onerror = () => {
            urlPreview.style.display = 'none';
            alert('无法加载图片，请检查URL是否正确');
        };

        urlPreviewImg.src = imageUrl;
    }

    insertSelectedImage() {
        if (this.selectedImageData) {
            this.insertImageData(this.selectedImageData);
            document.querySelector('.image-upload-modal').remove();
        }
    }

    insertUrlImage() {
        const imageUrl = document.querySelector('#imageUrl').value.trim();
        const imageAlt = document.querySelector('#imageAlt').value.trim();

        if (!imageUrl) {
            alert('请输入图片URL');
            return;
        }

        this.insertImageData(imageUrl, imageAlt);
        document.querySelector('.image-upload-modal').remove();
    }

    insertImageData(src, alt = '') {
        // 恢复选择范围
        if (this.savedSelection) {
            const selection = window.getSelection();
            selection.removeAllRanges();
            selection.addRange(this.savedSelection);
        }

        const img = `<img src="${src}" alt="${alt}" style="max-width: 100%; height: auto; display: block; margin: 10px 0;">`;
        document.execCommand('insertHTML', false, img);

        this.editor.focus();
        this.updateContent();
        this.scheduleAutoSave();
    }

    insertImage(file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            this.insertImageData(e.target.result, file.name);
        };
        reader.readAsDataURL(file);
    }

    insertCodeBlock() {
        const code = prompt('请输入代码:');
        if (code) {
            const codeBlock = `<pre><code>${this.escapeHtml(code)}</code></pre>`;
            document.execCommand('insertHTML', false, codeBlock);
        }
    }

    toggleBlockquote() {
        const selection = window.getSelection();
        const range = selection.getRangeAt(0);
        const container = range.commonAncestorContainer;

        // 查找是否在引用块中
        let blockquote = container.nodeType === Node.TEXT_NODE ?
            container.parentElement.closest('blockquote') :
            container.closest('blockquote');

        if (blockquote) {
            // 移除引用格式
            const content = blockquote.innerHTML;
            blockquote.outerHTML = `<p>${content}</p>`;
        } else {
            // 添加引用格式
            document.execCommand('formatBlock', false, 'blockquote');
        }
    }

    showColorPicker(command) {
        // 简化的颜色选择器实现
        const colors = [
            '#000000', '#333333', '#666666', '#999999',
            '#ff0000', '#00ff00', '#0000ff', '#ffff00',
            '#ff00ff', '#00ffff', '#ffa500', '#800080'
        ];

        const color = prompt('请选择颜色 (hex格式，如 #ff0000):');
        if (color && /^#[0-9A-F]{6}$/i.test(color)) {
            document.execCommand(command, false, color);
        }
    }

    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key.toLowerCase()) {
                case 'b':
                    e.preventDefault();
                    this.executeCommand('bold');
                    break;
                case 'i':
                    e.preventDefault();
                    this.executeCommand('italic');
                    break;
                case 'u':
                    e.preventDefault();
                    this.executeCommand('underline');
                    break;
                case 'k':
                    e.preventDefault();
                    this.executeCommand('createLink');
                    break;
            }
        }
    }

    handlePaste(e) {
        e.preventDefault();
        const text = (e.clipboardData || window.clipboardData).getData('text/plain');
        document.execCommand('insertText', false, text);
    }

    handleImageDrop(e) {
        const files = Array.from(e.dataTransfer.files);
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        imageFiles.forEach(file => {
            this.insertImage(file);
        });
    }

    updateContent() {
        this.content = this.editor.innerHTML;
        if (this.options.onChange) {
            this.options.onChange(this.content);
        }
    }

    updateToolbarState() {
        const buttons = this.toolbar.querySelectorAll('.toolbar-button');
        buttons.forEach(button => {
            const command = button.dataset.command;
            if (['bold', 'italic', 'underline', 'strikeThrough'].includes(command)) {
                button.classList.toggle('active', document.queryCommandState(command));
            }
        });
    }

    setupAutoSave() {
        // 自动保存功能的实现将在后续添加
    }

    scheduleAutoSave() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        this.autoSaveTimer = setTimeout(() => {
            if (this.options.onAutoSave) {
                this.options.onAutoSave(this.content);
            }
        }, this.options.autoSaveDelay);
    }

    getContent() {
        return this.editor.innerHTML;
    }

    setContent(html) {
        this.editor.innerHTML = html;
        this.content = html;
    }

    getPlainText() {
        return this.editor.textContent || this.editor.innerText || '';
    }

    focus() {
        this.editor.focus();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    destroy() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        this.container.innerHTML = '';
    }
}

// 导出到全局
window.RichTextEditor = RichTextEditor;
