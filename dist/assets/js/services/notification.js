// 通知服务
class NotificationService {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.maxNotifications = 5;
        this.defaultDuration = 3000;
        this.init();
    }

    // 初始化通知容器
    init() {
        this.container = document.createElement('div');
        this.container.id = 'notification-container';
        this.container.className = 'fixed top-4 right-4 z-50 space-y-2';
        document.body.appendChild(this.container);
    }

    // 显示通知
    show(message, type = 'info', options = {}) {
        const notification = this.createNotification(message, type, options);
        this.addNotification(notification);
        return notification.id;
    }

    // 创建通知元素
    createNotification(message, type, options) {
        const id = Utils.String.uuid();
        const duration = options.duration || this.defaultDuration;
        const persistent = options.persistent || false;
        const actions = options.actions || [];

        const notification = {
            id,
            message,
            type,
            duration,
            persistent,
            actions,
            element: null,
            timer: null
        };

        // 创建DOM元素
        const element = document.createElement('div');
        element.className = `notification ${type} transform transition-all duration-300 ease-in-out`;
        element.innerHTML = this.getNotificationHTML(notification);
        
        notification.element = element;

        // 绑定事件
        this.bindNotificationEvents(notification);

        return notification;
    }

    // 获取通知HTML
    getNotificationHTML(notification) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };

        const icon = icons[notification.type] || icons.info;
        
        let actionsHTML = '';
        if (notification.actions.length > 0) {
            actionsHTML = `
                <div class="notification-actions mt-2 flex gap-2">
                    ${notification.actions.map(action => `
                        <button 
                            class="btn btn-sm ${action.primary ? 'btn-primary' : 'btn-secondary'}"
                            data-action="${action.id}"
                        >
                            ${action.label}
                        </button>
                    `).join('')}
                </div>
            `;
        }

        return `
            <div class="notification-header">
                <span class="notification-icon">${icon}</span>
                <div class="notification-content">
                    <div class="notification-message">${notification.message}</div>
                    ${actionsHTML}
                </div>
                ${!notification.persistent ? `
                    <button class="notification-close" data-action="close">
                        ✕
                    </button>
                ` : ''}
            </div>
        `;
    }

    // 绑定通知事件
    bindNotificationEvents(notification) {
        const element = notification.element;

        // 关闭按钮
        const closeBtn = element.querySelector('[data-action="close"]');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.remove(notification.id);
            });
        }

        // 动作按钮
        const actionBtns = element.querySelectorAll('[data-action]:not([data-action="close"])');
        actionBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const actionId = e.target.dataset.action;
                const action = notification.actions.find(a => a.id === actionId);
                if (action && action.handler) {
                    action.handler(notification);
                }
                if (action && action.autoClose !== false) {
                    this.remove(notification.id);
                }
            });
        });

        // 鼠标悬停暂停自动关闭
        element.addEventListener('mouseenter', () => {
            if (notification.timer) {
                clearTimeout(notification.timer);
                notification.timer = null;
            }
        });

        element.addEventListener('mouseleave', () => {
            if (!notification.persistent && notification.duration > 0) {
                this.setAutoClose(notification);
            }
        });
    }

    // 添加通知到容器
    addNotification(notification) {
        // 限制通知数量
        if (this.notifications.length >= this.maxNotifications) {
            this.remove(this.notifications[0].id);
        }

        this.notifications.push(notification);
        this.container.appendChild(notification.element);

        // 触发入场动画
        requestAnimationFrame(() => {
            notification.element.style.transform = 'translateX(0)';
            notification.element.style.opacity = '1';
        });

        // 设置自动关闭
        if (!notification.persistent && notification.duration > 0) {
            this.setAutoClose(notification);
        }

        // 触发事件
        this.emit('show', notification);
    }

    // 设置自动关闭
    setAutoClose(notification) {
        notification.timer = setTimeout(() => {
            this.remove(notification.id);
        }, notification.duration);
    }

    // 移除通知
    remove(id) {
        const index = this.notifications.findIndex(n => n.id === id);
        if (index === -1) return;

        const notification = this.notifications[index];
        
        // 清除定时器
        if (notification.timer) {
            clearTimeout(notification.timer);
        }

        // 触发退场动画
        notification.element.style.transform = 'translateX(100%)';
        notification.element.style.opacity = '0';

        // 动画完成后移除元素
        setTimeout(() => {
            if (notification.element.parentNode) {
                notification.element.parentNode.removeChild(notification.element);
            }
            this.notifications.splice(index, 1);
            this.emit('remove', notification);
        }, 300);
    }

    // 清空所有通知
    clear() {
        this.notifications.forEach(notification => {
            this.remove(notification.id);
        });
    }

    // 快捷方法
    success(message, options = {}) {
        return this.show(message, 'success', options);
    }

    error(message, options = {}) {
        return this.show(message, 'error', { 
            duration: 5000, 
            ...options 
        });
    }

    warning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    info(message, options = {}) {
        return this.show(message, 'info', options);
    }

    // 确认对话框
    confirm(message, options = {}) {
        return new Promise((resolve) => {
            const actions = [
                {
                    id: 'cancel',
                    label: options.cancelText || '取消',
                    handler: () => resolve(false)
                },
                {
                    id: 'confirm',
                    label: options.confirmText || '确认',
                    primary: true,
                    handler: () => resolve(true)
                }
            ];

            this.show(message, 'warning', {
                persistent: true,
                actions,
                ...options
            });
        });
    }

    // 输入对话框
    prompt(message, options = {}) {
        return new Promise((resolve) => {
            const inputId = Utils.String.uuid();
            const messageWithInput = `
                ${message}
                <div class="mt-2">
                    <input 
                        type="text" 
                        id="${inputId}"
                        class="form-input"
                        placeholder="${options.placeholder || ''}"
                        value="${options.defaultValue || ''}"
                    >
                </div>
            `;

            const actions = [
                {
                    id: 'cancel',
                    label: options.cancelText || '取消',
                    handler: () => resolve(null)
                },
                {
                    id: 'confirm',
                    label: options.confirmText || '确认',
                    primary: true,
                    handler: () => {
                        const input = document.getElementById(inputId);
                        resolve(input ? input.value : '');
                    }
                }
            ];

            this.show(messageWithInput, 'info', {
                persistent: true,
                actions,
                ...options
            });

            // 聚焦输入框
            setTimeout(() => {
                const input = document.getElementById(inputId);
                if (input) {
                    input.focus();
                    input.select();
                }
            }, 100);
        });
    }

    // 进度通知
    progress(message, options = {}) {
        const progressId = Utils.String.uuid();
        const messageWithProgress = `
            ${message}
            <div class="mt-2">
                <div class="progress">
                    <div id="${progressId}" class="progress-bar" style="width: 0%"></div>
                </div>
            </div>
        `;

        const notificationId = this.show(messageWithProgress, 'info', {
            persistent: true,
            ...options
        });

        return {
            id: notificationId,
            update: (percentage) => {
                const progressBar = document.getElementById(progressId);
                if (progressBar) {
                    progressBar.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
                }
            },
            complete: (message) => {
                if (message) {
                    this.success(message);
                }
                this.remove(notificationId);
            },
            error: (message) => {
                if (message) {
                    this.error(message);
                }
                this.remove(notificationId);
            }
        };
    }

    // 事件系统
    emit(event, data) {
        const customEvent = new CustomEvent(`notification:${event}`, {
            detail: data
        });
        document.dispatchEvent(customEvent);
    }

    // 监听事件
    on(event, handler) {
        document.addEventListener(`notification:${event}`, handler);
    }

    // 移除事件监听
    off(event, handler) {
        document.removeEventListener(`notification:${event}`, handler);
    }
}

// 创建全局实例
window.notification = new NotificationService();

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { NotificationService, notification: window.notification };
}
